# config/top_symbols.py
# Top 20 cryptocurrency trading pairs by volume and market cap
# Updated: 2024-06-28

# Top 20 USDT pairs by trading volume and market cap
TOP_20_SYMBOLS = [
    "BTCUSDT",      # Bitcoin - King of crypto
    "ETHUSDT",      # Ethereum - Smart contracts leader
    "BNBUSDT",      # Binance Coin - Exchange token
    "SOLUSDT",      # Solana - High performance blockchain
    "XRPUSDT",      # Ripple - Cross-border payments
    "ADAUSDT",      # Cardano - Academic blockchain
    "AVAXUSDT",     # Avalanche - Fast consensus
    "DOGEUSDT",     # Dogecoin - Meme coin leader
    "DOTUSDT",      # Polkadot - Interoperability
    "MATICUSDT",    # Polygon - Ethereum scaling
    "LINKUSDT",     # Chainlink - Oracle network
    "LTCUSDT",      # Litecoin - Digital silver
    "UNIUSDT",      # Uniswap - DEX leader
    "ATOMUSDT",     # Cosmos - Internet of blockchains
    "FILUSDT",      # Filecoin - Decentralized storage
    "TRXUSDT",      # Tron - Entertainment blockchain
    "ETCUSDT",      # Ethereum Classic - Original Ethereum
    "XLMUSDT",      # Stellar - Cross-border payments
    "VETUSDT",      # VeChain - Supply chain
    "ICPUSDT"       # Internet Computer - Web3 infrastructure
]

# Alternative symbols if some are not available
BACKUP_SYMBOLS = [
    "SHIBUSDT",     # Shiba Inu - Meme coin
    "NEARUSDT",     # Near Protocol - Developer friendly
    "ALGOUSDT",     # Algorand - Pure proof of stake
    "SANDUSDT",     # The Sandbox - Gaming metaverse
    "MANAUSDT",     # Decentraland - Virtual reality
    "FTMUSDT",      # Fantom - Fast blockchain
    "HBARUSDT",     # Hedera - Enterprise blockchain
    "FLOWUSDT",     # Flow - NFT blockchain
    "EGLDUSDT",     # MultiversX - Internet scale blockchain
    "THETAUSDT"     # Theta - Video streaming
]

# Trading intervals for analysis
INTERVALS = ["5m", "15m", "1h"]

# Model parameters
MODEL_CONFIG = {
    "n_past": 30,           # Look back period
    "n_epochs": 10,         # Training epochs (reduced for faster training)
    "batch_size": 32,       # Batch size for training
    "validation_split": 0.2, # Validation data percentage
    "features": ["close", "rsi", "ma", "macd"],  # Technical indicators
    "min_diff_pct": 0.3,    # Minimum price difference for signal
    "limit": 200            # Number of candles to fetch
}

# Risk management settings
RISK_CONFIG = {
    "balance": 1000,        # Starting balance
    "max_position_size": 0.1, # Max 10% per trade
    "stop_loss_pct": 2.0,   # 2% stop loss
    "take_profit_pct": 4.0  # 4% take profit
}

def get_active_symbols():
    """Get list of active trading symbols"""
    return TOP_20_SYMBOLS

def get_backup_symbols():
    """Get backup symbols if primary ones fail"""
    return BACKUP_SYMBOLS

def get_all_symbols():
    """Get all available symbols"""
    return TOP_20_SYMBOLS + BACKUP_SYMBOLS

def validate_symbol_availability(symbols_list):
    """Validate which symbols are available for trading"""
    import requests
    
    available_symbols = []
    try:
        # Get all trading symbols from Binance
        url = "https://api.binance.com/api/v3/exchangeInfo"
        resp = requests.get(url, timeout=10)
        resp.raise_for_status()
        data = resp.json()
        
        active_symbols = {s["symbol"] for s in data["symbols"] 
                         if s["quoteAsset"] == "USDT" and s["status"] == "TRADING"}
        
        # Filter our list to only include active symbols
        for symbol in symbols_list:
            if symbol in active_symbols:
                available_symbols.append(symbol)
            else:
                print(f"[WARNING] Symbol {symbol} not available for trading")
                
    except Exception as e:
        print(f"[ERROR] Failed to validate symbols: {e}")
        return symbols_list  # Return original list if validation fails
    
    return available_symbols

def get_validated_top_symbols(count=20):
    """Get validated list of top symbols"""
    all_symbols = get_all_symbols()
    validated = validate_symbol_availability(all_symbols)
    return validated[:count]
