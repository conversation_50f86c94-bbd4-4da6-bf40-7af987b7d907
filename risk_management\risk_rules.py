# risk_management/risk_rules.py
# Module quản lý rủi ro: kiểm tra SL/TP, DCA, position sizing cho tín hiệu trade

def check_stop_loss_take_profit(signal: dict, sl_pct: float = 1.5, tp_pct: float = 2.0):
    """
    Với mỗi tín hiệu, sinh c<PERSON>c mức giá SL/TP cố định dựa vào last_close:
    - SL: last_close -/+ sl_pct% (tùy BUY/SELL)
    - TP: last_close +/+ tp_pct%
    """
    last_close = signal.get("last_close")
    direction = signal.get("signal")
    if last_close is None or direction not in ("BUY", "SELL"):
        return None
    if direction == "BUY":
        sl = last_close * (1 - sl_pct/100)
        tp = last_close * (1 + tp_pct/100)
    else: # SELL
        sl = last_close * (1 + sl_pct/100)
        tp = last_close * (1 - tp_pct/100)
    return {"stop_loss": sl, "take_profit": tp}

def check_position_size(balance: float, risk_pct: float = 1.0, sl_pct: float = 1.5):
    """
    Quản lý khối lượng lệnh dựa vào rủi ro tài khoản:
    - balance: số dư hiện tại (USD)
    - risk_pct: % tài khoản tối đa chịu rủi ro trên 1 lệnh
    - sl_pct: % stoploss của lệnh đó
    """
    risk_amount = balance * (risk_pct / 100)
    # Khối lượng vào lệnh = số tiền rủi ro chia cho SL (tính %)
    position_size = risk_amount / (sl_pct / 100)
    return max(0, position_size)
