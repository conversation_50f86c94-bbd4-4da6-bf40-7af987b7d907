# data_analysis/price_prediction.py
# <PERSON><PERSON><PERSON> cả single-feature (chỉ close) và multi-feature (close, RSI, MA, MACD...)
import pandas as pd
import numpy as np
from typing import List, Optional
import os

MODEL_PATH = "data_analysis/lstm_saved_model.keras"
MODEL_PATH_MULTI = "data_analysis/lstm_multi_feature.keras"

# ==== SINGLE FEATURE (cũ, chỉ close) ====
def train_and_save_lstm(closes: List[float], n_past: int = 30, n_epochs: int = 30) -> str:
    import tensorflow as tf
    from keras import layers, models
    arr = np.array(closes)
    arr_norm = (arr - arr.min()) / (arr.max() - arr.min() + 1e-8)
    X, y = [], []
    for i in range(len(arr_norm) - n_past):
        X.append(arr_norm[i:i+n_past])
        y.append(arr_norm[i+n_past])
    X, y = np.array(X), np.array(y)
    X = X.reshape((X.shape[0], X.shape[1], 1))
    model = models.Sequential([
        layers.LSTM(32, input_shape=(n_past,1)),
        layers.Dense(1)
    ])
    model.compile(optimizer='adam', loss='mse')
    model.fit(X, y, epochs=n_epochs, verbose=0)
    model.save(MODEL_PATH)
    return MODEL_PATH

def load_and_predict_lstm(closes: List[float], n_past: int = 30) -> Optional[float]:
    import tensorflow as tf
    if not os.path.exists(MODEL_PATH):
        return None
    model = tf.keras.models.load_model(MODEL_PATH)
    arr = np.array(closes)
    arr_norm = (arr - arr.min()) / (arr.max() - arr.min() + 1e-8)
    if len(arr_norm) < n_past:
        return None
    last_seq = arr_norm[-n_past:].reshape((1, n_past, 1))
    pred_norm = model.predict(last_seq, verbose=0)[0][0]
    pred = pred_norm * (arr.max() - arr.min()) + arr.min()
    return float(pred)

# ==== MULTI FEATURE (close, rsi, ma, macd...) ====
def extract_features(df: pd.DataFrame, features: List[str]) -> np.ndarray:
    arrs = []
    for f in features:
        if f in df:
            arrs.append(df[f].values.reshape(-1, 1))
    return np.concatenate(arrs, axis=1) if arrs else None

def validate_data_for_training(df: pd.DataFrame, features: List[str], n_past: int = 30) -> bool:
    """Validate data quality before training"""
    if df.empty:
        return False

    # Check if all features exist
    missing_features = [f for f in features if f not in df.columns]
    if missing_features:
        print(f"[WARNING] Missing features: {missing_features}")
        return False

    # Check for sufficient data
    if len(df) < n_past + 10:
        print(f"[WARNING] Insufficient data: {len(df)} rows, need at least {n_past + 10}")
        return False

    # Check for too many NaN values
    for feature in features:
        nan_pct = df[feature].isna().sum() / len(df)
        if nan_pct > 0.3:  # More than 30% NaN
            print(f"[WARNING] Too many NaN values in {feature}: {nan_pct:.1%}")
            return False

    return True

def train_and_save_lstm_multi(df: pd.DataFrame, features: List[str], n_past: int = 30, n_epochs: int = 20) -> str:
    import tensorflow as tf
    from keras import Input, layers, models
    import logging

    # Validate data first
    if not validate_data_for_training(df, features, n_past):
        return None

    # Clean data
    df_clean = df.dropna()
    if len(df_clean) < n_past + 10:
        print(f"[WARNING] Not enough clean data: {len(df_clean)}")
        return None

    try:
        arr = extract_features(df_clean, features)
        if arr is None or arr.shape[0] < n_past + 1:
            return None

        # Normalize data with better handling
        arr_min = arr.min(axis=0)
        arr_max = arr.max(axis=0)
        arr_range = arr_max - arr_min
        arr_range[arr_range == 0] = 1  # Avoid division by zero
        arr_norm = (arr - arr_min) / (arr_range + 1e-8)

        X, y = [], []
        for i in range(len(arr_norm) - n_past):
            X.append(arr_norm[i:i+n_past])
            y.append(arr_norm[i+n_past, 0])  # Dự đoán giá close

        X, y = np.array(X), np.array(y)

        # Create model with Input layer to avoid warnings
        model = models.Sequential([
            Input(shape=(n_past, len(features))),
            layers.LSTM(32, return_sequences=False),
            layers.Dropout(0.2),  # Add dropout for regularization
            layers.Dense(1, activation='linear')
        ])

        model.compile(optimizer='adam', loss='mse', metrics=['mae'])

        # Train with validation split
        history = model.fit(
            X, y,
            epochs=n_epochs,
            verbose=0,
            validation_split=0.2,
            batch_size=32
        )

        model.save(MODEL_PATH_MULTI)
        print(f"[INFO] Model trained successfully. Final loss: {history.history['loss'][-1]:.6f}")
        return MODEL_PATH_MULTI

    except Exception as e:
        print(f"[ERROR] Training failed: {e}")
        return None

def load_and_predict_lstm_multi(df: pd.DataFrame, features: List[str], n_past: int = 30) -> Optional[float]:
    import tensorflow as tf

    # Check if model exists
    if not os.path.exists(MODEL_PATH_MULTI):
        print(f"[WARNING] Model not found: {MODEL_PATH_MULTI}")
        return None

    # Validate input data
    if not validate_data_for_training(df, features, n_past):
        return None

    try:
        # Clean data
        df_clean = df.dropna()
        if len(df_clean) < n_past:
            print(f"[WARNING] Not enough clean data for prediction: {len(df_clean)}")
            return None

        arr = extract_features(df_clean, features)
        if arr is None:
            return None

        # Normalize with same logic as training
        arr_min = arr.min(axis=0)
        arr_max = arr.max(axis=0)
        arr_range = arr_max - arr_min
        arr_range[arr_range == 0] = 1  # Avoid division by zero
        arr_norm = (arr - arr_min) / (arr_range + 1e-8)

        if arr_norm.shape[0] < n_past:
            return None

        # Load model and predict
        model = tf.keras.models.load_model(MODEL_PATH_MULTI)
        last_seq = arr_norm[-n_past:].reshape((1, n_past, len(features)))
        pred_norm = model.predict(last_seq, verbose=0)[0][0]

        # Scale back to original price range
        close_min = arr[:,0].min()
        close_max = arr[:,0].max()
        close_range = close_max - close_min
        if close_range == 0:
            close_range = 1

        pred = pred_norm * close_range + close_min

        # Sanity check
        if np.isnan(pred) or np.isinf(pred):
            print(f"[WARNING] Invalid prediction: {pred}")
            return None

        return float(pred)

    except Exception as e:
        print(f"[ERROR] Prediction failed: {e}")
        return None
