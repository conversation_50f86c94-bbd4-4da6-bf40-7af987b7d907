# data_analysis/price_prediction.py
# <PERSON><PERSON><PERSON> cả single-feature (chỉ close) và multi-feature (close, RSI, MA, MACD...)
import pandas as pd
import numpy as np
from typing import List, Optional
import os

MODEL_PATH = "data_analysis/lstm_saved_model.keras"
MODEL_PATH_MULTI = "data_analysis/lstm_multi_feature.keras"

# ==== SINGLE FEATURE (cũ, chỉ close) ====
def train_and_save_lstm(closes: List[float], n_past: int = 30, n_epochs: int = 30) -> str:
    import tensorflow as tf
    from keras import layers, models
    arr = np.array(closes)
    arr_norm = (arr - arr.min()) / (arr.max() - arr.min() + 1e-8)
    X, y = [], []
    for i in range(len(arr_norm) - n_past):
        X.append(arr_norm[i:i+n_past])
        y.append(arr_norm[i+n_past])
    X, y = np.array(X), np.array(y)
    X = X.reshape((X.shape[0], X.shape[1], 1))
    model = models.Sequential([
        layers.LSTM(32, input_shape=(n_past,1)),
        layers.Dense(1)
    ])
    model.compile(optimizer='adam', loss='mse')
    model.fit(X, y, epochs=n_epochs, verbose=0)
    model.save(MODEL_PATH)
    return MODEL_PATH

def load_and_predict_lstm(closes: List[float], n_past: int = 30) -> Optional[float]:
    import tensorflow as tf
    if not os.path.exists(MODEL_PATH):
        return None
    model = tf.keras.models.load_model(MODEL_PATH)
    arr = np.array(closes)
    arr_norm = (arr - arr.min()) / (arr.max() - arr.min() + 1e-8)
    if len(arr_norm) < n_past:
        return None
    last_seq = arr_norm[-n_past:].reshape((1, n_past, 1))
    pred_norm = model.predict(last_seq, verbose=0)[0][0]
    pred = pred_norm * (arr.max() - arr.min()) + arr.min()
    return float(pred)

# ==== MULTI FEATURE (close, rsi, ma, macd...) ====
def extract_features(df: pd.DataFrame, features: List[str]) -> np.ndarray:
    arrs = []
    for f in features:
        if f in df:
            arrs.append(df[f].values.reshape(-1, 1))
    return np.concatenate(arrs, axis=1) if arrs else None

def train_and_save_lstm_multi(df: pd.DataFrame, features: List[str], n_past: int = 30, n_epochs: int = 20) -> str:
    import tensorflow as tf
    from keras import layers, models
    arr = extract_features(df, features)
    if arr is None or arr.shape[0] < n_past + 1:
        return None
    arr_norm = (arr - arr.min(axis=0)) / (arr.max(axis=0) - arr.min(axis=0) + 1e-8)
    X, y = [], []
    for i in range(len(arr_norm) - n_past):
        X.append(arr_norm[i:i+n_past])
        y.append(arr_norm[i+n_past, 0])  # Dự đoán giá close
    X, y = np.array(X), np.array(y)
    model = models.Sequential([
        layers.LSTM(32, input_shape=(n_past, len(features))),
        layers.Dense(1)
    ])
    model.compile(optimizer='adam', loss='mse')
    model.fit(X, y, epochs=n_epochs, verbose=0)
    model.save(MODEL_PATH_MULTI)
    return MODEL_PATH_MULTI

def load_and_predict_lstm_multi(df: pd.DataFrame, features: List[str], n_past: int = 30) -> Optional[float]:
    import tensorflow as tf
    if not os.path.exists(MODEL_PATH_MULTI):
        return None
    arr = extract_features(df, features)
    arr_norm = (arr - arr.min(axis=0)) / (arr.max(axis=0) - arr.min(axis=0) + 1e-8)
    if arr_norm.shape[0] < n_past:
        return None
    model = tf.keras.models.load_model(MODEL_PATH_MULTI)
    last_seq = arr_norm[-n_past:].reshape((1, n_past, len(features)))
    pred_norm = model.predict(last_seq, verbose=0)[0][0]
    # Scale ngược về giá close gốc
    close_min = arr[:,0].min()
    close_max = arr[:,0].max()
    pred = pred_norm * (close_max - close_min) + close_min
    return float(pred)
