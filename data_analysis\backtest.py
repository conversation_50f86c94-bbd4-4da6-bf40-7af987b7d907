# data_analysis/backtest.py
# <PERSON><PERSON><PERSON> giá hiệu quả các tín hiệu giao dịch đã lưu trong DB
from db.session import get_database
import pandas as pd

def backtest_signals(symbol: str, interval: str, n_recent: int = 100):
    """
    Lấy n_recent tín hiệu trade gần nhất, ki<PERSON><PERSON> tra có hit SL/TP không dựa theo dữ liệu nến thực tế.
    Tr<PERSON> về winrate, lợ<PERSON> nhuận tổng, log từng lệnh.
    """
    db = get_database()
    trade_col = db['trade_signals']
    kline_col = db[f'kline_{symbol.lower()}_{interval}']
    # Lấy các trade gần nhất
    trades = list(trade_col.find({"symbol": symbol, "interval": interval}).sort("created_at", -1).limit(n_recent))
    if not trades:
        return {"error": "No signals to backtest"}
    trades = trades[::-1]  # Thứ tự tăng dần thời gian
    # <PERSON><PERSON><PERSON> kết quả từng lệnh
    results = []
    win_count = 0
    loss_count = 0
    profit_sum = 0.0
    for trade in trades:
        open_time = trade["created_at"]
        direction = trade["signal"]
        entry = trade["last_close"]
        sl = trade["stop_loss"]
        tp = trade["take_profit"]
        pos_size = trade["position_size"]
        # Lấy max/min giá nến sau khi mở lệnh (ví dụ: 20 nến kế tiếp)
        klines = list(kline_col.find({"open_time": {"$gt": open_time}}).sort("open_time", 1).limit(20))
        if not klines:
            continue
        price_hit = None
        for k in klines:
            high = k["high"]
            low = k["low"]
            if direction == "BUY":
                if high >= tp:
                    price_hit = "TP"
                    break
                if low <= sl:
                    price_hit = "SL"
                    break
            else:  # SELL
                if low <= tp:
                    price_hit = "TP"
                    break
                if high >= sl:
                    price_hit = "SL"
                    break
        if price_hit == "TP":
            win_count += 1
            profit = abs(tp - entry) * pos_size / entry
        elif price_hit == "SL":
            loss_count += 1
            profit = -abs(sl - entry) * pos_size / entry
        else:
            profit = 0.0  # Không hit
        profit_sum += profit
        results.append({
            "entry_time": open_time,
            "signal": direction,
            "entry": entry,
            "stop_loss": sl,
            "take_profit": tp,
            "result": price_hit,
            "profit": profit
        })
    winrate = win_count / (win_count + loss_count) * 100 if (win_count + loss_count) > 0 else 0
    return {
        "symbol": symbol,
        "interval": interval,
        "n_trades": len(results),
        "winrate": winrate,
        "total_profit": profit_sum,
        "detail": results
    }
