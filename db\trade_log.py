# db/trade_log.py
# <PERSON><PERSON><PERSON> (signal), stoploss, takeprofit, kết qu<PERSON> backtest vào MongoDB
from db.session import get_database
import time

def log_trade_signal(symbol, interval, signal, last_close, prediction, sl, tp, pos_size, backtest_result=None):
    db = get_database()
    collection = db['trade_signals']
    log = {
        "symbol": symbol,
        "interval": interval,
        "signal": signal,
        "last_close": last_close,
        "prediction": prediction,
        "stop_loss": sl,
        "take_profit": tp,
        "position_size": pos_size,
        "backtest_result": backtest_result,
        "created_at": int(time.time()*1000)
    }
    collection.insert_one(log)
    return log

def get_trade_signals(symbol=None, interval=None, limit=100):
    db = get_database()
    collection = db['trade_signals']
    query = {}
    if symbol:
        query['symbol'] = symbol
    if interval:
        query['interval'] = interval
    cursor = collection.find(query).sort('created_at', -1).limit(limit)
    return list(cursor)
