# scheduler/train_lstm_multi_scheduler.py
# Scheduler train model LSTM đa biến (multi-feature: close, rsi, ma, macd...)
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import time
from apscheduler.schedulers.background import BackgroundScheduler
from data_analysis.price_prediction import train_and_save_lstm_multi
from db.session import get_database
import pandas as pd

SYMBOLS = ["BTCUSDT", "ETHUSDT", "BNBUSDT"]
INTERVALS = ["5m", "15m"]
LIMIT = 300
N_PAST = 30
N_EPOCHS = 30
FEATURES = ["close", "rsi", "ma", "macd"]

# Auto tính RSI, MA, MACD
def add_indicators(df, rsi_period=14, ma_period=14, macd_fast=12, macd_slow=26, macd_signal=9):
    if df.empty or "close" not in df:
        return df
    closes = df['close']
    # RSI
    delta = closes.diff()
    gain = (delta.where(delta > 0, 0)).rolling(rsi_period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(rsi_period).mean()
    rs = gain / loss
    df['rsi'] = 100 - (100 / (1 + rs))
    # MA
    df['ma'] = closes.rolling(ma_period).mean()
    # MACD
    exp1 = closes.ewm(span=macd_fast, adjust=False).mean()
    exp2 = closes.ewm(span=macd_slow, adjust=False).mean()
    df['macd'] = exp1 - exp2
    return df

def retrain_lstm_multi_job():
    db = get_database()
    for symbol in SYMBOLS:
        for interval in INTERVALS:
            collection_name = f"kline_{symbol.lower()}_{interval}"
            collection = db[collection_name]
            data = list(collection.find().sort('open_time', -1).limit(LIMIT))
            data = data[::-1]
            df = pd.DataFrame(data)
            if df.empty or "close" not in df:
                print(f"[LSTM-Multi] {symbol}-{interval}: Không có dữ liệu để train.")
                continue
            # Thêm indicator
            df = add_indicators(df)
            path = train_and_save_lstm_multi(df, FEATURES, n_past=N_PAST, n_epochs=N_EPOCHS)
            print(f"[LSTM-Multi] Model retrained & saved for {symbol}-{interval}: {path}")

if __name__ == "__main__":
    scheduler = BackgroundScheduler()
    scheduler.add_job(retrain_lstm_multi_job, 'interval', hours=4, next_run_time=None)
    scheduler.start()
    print("[LSTM-Multi] Đang chạy... (ấn Ctrl+C để dừng)")
    try:
        while True:
            time.sleep(60)
    except (KeyboardInterrupt, SystemExit):
        scheduler.shutdown()
        print("[LSTM-Multi] Đã dừng.")
