from fastapi import APIRouter, Query
from fastapi import Body
import requests
from db.session import get_database
from bson import ObjectId
from data_analysis.price_prediction import train_and_save_lstm, load_and_predict_lstm ,load_and_predict_lstm_multi


import pandas as pd
from db.trade_log import log_trade_signal, get_trade_signals
router = APIRouter()

@router.get("/price")
def get_binance_price(symbol: str = "BTCUSDT"):
    """
    Lấy giá spot real-time từ Binance (public REST API).
    symbol: Mã cặp coin (vd: BTCUSDT, ETHUSDT)
    """
    url = f"https://api.binance.com/api/v3/ticker/price?symbol={symbol.upper()}"
    resp = requests.get(url)
    if resp.status_code == 200:
        data = resp.json()
        return {"symbol": data["symbol"], "price": float(data["price"])}
    return {"error": "Failed to get price from Binance"}

@router.get("/prices")
def get_binance_prices(symbols: list[str] = Query(..., description="Danh sách symbol: BTCUSDT,ETHUSDT")):
    """
    Lấy giá spot real-time nhiều cặp coin từ Binance.
    symbols: danh sách symbol (vd: BTCUSDT,ETHUSDT)
    """
    results = []
    for symbol in symbols:
        url = f"https://api.binance.com/api/v3/ticker/price?symbol={symbol.upper()}"
        resp = requests.get(url)
        if resp.status_code == 200:
            data = resp.json()
            results.append({"symbol": data["symbol"], "price": float(data["price"]), "success": True})
        else:
            results.append({"symbol": symbol.upper(), "error": "Failed to get price", "success": False})
    return {"data": results}
@router.get("/predict-multi")
def api_predict_multi(
    symbol: str = Query("BTCUSDT"),
    interval: str = Query("5m"),
    limit: int = Query(100),
    n_past: int = Query(30),
    features: str = Query("close,rsi,ma,macd")
):
    db = get_database()
    collection_name = f"kline_{symbol.lower()}_{interval}"
    collection = db[collection_name]
    data = list(collection.find().sort('open_time', -1).limit(limit))
    data = data[::-1]
    df = pd.DataFrame(data)
    feature_list = [f.strip() for f in features.split(",")]
    pred = load_and_predict_lstm_multi(df, feature_list, n_past=n_past)
    last_data = {f: float(df.iloc[-1][f]) if f in df.columns else None for f in feature_list}
    return {
        "symbol": symbol.upper(),
        "interval": interval,
        "n_past": n_past,
        "features": feature_list,
        "last_data": last_data,
        "prediction": pred
    }
def serialize_mongo(doc):
    """Chuyển đổi ObjectId thành string (nếu có)."""
    if isinstance(doc, list):
        return [serialize_mongo(d) for d in doc]
    elif isinstance(doc, dict):
        new_doc = {}
        for k, v in doc.items():
            if isinstance(v, ObjectId):
                new_doc[k] = str(v)
            else:
                new_doc[k] = serialize_mongo(v)
        return new_doc
    else:
        return doc
@router.get("/kline")
def get_kline(symbol: str = "BTCUSDT", interval: str = "1m", limit: int = 100, save: bool = False):
    """
    Lấy dữ liệu nến (candlestick) từ Binance cho 1 cặp coin, tuỳ chọn lưu về MongoDB.
    symbol: mã cặp (BTCUSDT)
    interval: khung thời gian (1m, 5m, 1h, 1d...)
    limit: số lượng nến (tối đa 1000 theo API Binance)
    save: nếu True, lưu vào MongoDB (dataBot.kline_{symbol}_{interval})
    """
    url = f"https://api.binance.com/api/v3/klines?symbol={symbol.upper()}&interval={interval}&limit={limit}"
    resp = requests.get(url)
    if resp.status_code == 200:
        klines = resp.json()
        formatted = [
            {
                "open_time": k[0],
                "open": float(k[1]),
                "high": float(k[2]),
                "low": float(k[3]),
                "close": float(k[4]),
                "volume": float(k[5]),
                "close_time": k[6],
                "symbol": symbol.upper(),
                "interval": interval
            }
            for k in klines
        ]
        if save:
            db = get_database()
            collection_name = f"kline_{symbol.lower()}_{interval}"
            collection = db[collection_name]
            collection.create_index([('open_time', 1)], unique=True)
            inserted = 0
            for item in formatted:
                try:
                    collection.insert_one(item)
                    inserted += 1
                except Exception:
                    pass
            # Truy vấn lại các nến vừa lưu và serialize ObjectId
            data_from_db = list(collection.find().sort('open_time', -1).limit(limit))
            return {
                "symbol": symbol.upper(),
                "interval": interval,
                "saved": inserted,
                "data": serialize_mongo(data_from_db)
            }
        return {"symbol": symbol.upper(), "interval": interval, "data": formatted}
    return {"error": "Failed to get kline data from Binance"}
@router.get("/kline-db")
def get_kline_db(symbol: str = "BTCUSDT", interval: str = "1m", limit: int = 100):
    """
    Lấy dữ liệu nến (candlestick) từ MongoDB đã lưu, phục vụ backtest hoặc hiển thị chart nhanh.
    """
    db = get_database()
    collection_name = f"kline_{symbol.lower()}_{interval}"
    collection = db[collection_name]
    data = list(collection.find().sort('open_time', -1).limit(limit))
    data = data[::-1]  # Trả về thứ tự tăng dần thời gian
    return {"symbol": symbol.upper(), "interval": interval, "data": serialize_mongo(data)}

# TÍNH CHỈ BÁO KỸ THUẬT RSI, MA
@router.get("/indicator")
def calc_indicator(
    symbol: str = "BTCUSDT",
    interval: str = "1m",
    limit: int = 100,
    rsi_period: int = 14,
    ma_period: int = 14,
    ema_period: int = 14,
    macd_fast: int = 12,
    macd_slow: int = 26,
    macd_signal: int = 9,
    bb_period: int = 20,
    bb_std: float = 2.0
):
    """
    Tính các chỉ báo kỹ thuật RSI, MA, EMA, MACD, Bollinger Bands dựa trên nến lấy từ MongoDB
    """
    db = get_database()
    collection_name = f"kline_{symbol.lower()}_{interval}"
    collection = db[collection_name]
    data = list(collection.find().sort('open_time', -1).limit(limit))
    data = data[::-1]
    df = pd.DataFrame(data)
    if df.empty or "close" not in df:
        return {"error": "No data to calculate indicator"}
    closes = df['close']
    # RSI
    delta = closes.diff()
    gain = (delta.where(delta > 0, 0)).rolling(rsi_period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(rsi_period).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    # MA (Moving Average)
    ma = closes.rolling(ma_period).mean()
    # EMA (Exponential MA)
    ema = closes.ewm(span=ema_period, adjust=False).mean()
    # MACD
    exp1 = closes.ewm(span=macd_fast, adjust=False).mean()
    exp2 = closes.ewm(span=macd_slow, adjust=False).mean()
    macd_line = exp1 - exp2
    macd_signal_line = macd_line.ewm(span=macd_signal, adjust=False).mean()
    macd_hist = macd_line - macd_signal_line
    # Bollinger Bands
    bb_ma = closes.rolling(bb_period).mean()
    bb_stddev = closes.rolling(bb_period).std()
    bb_upper = bb_ma + bb_std * bb_stddev
    bb_lower = bb_ma - bb_std * bb_stddev
    # Trả về dữ liệu kèm chỉ báo
    indicators = []
    for i in range(len(df)):
        indicators.append({
            "open_time": int(df.iloc[i]['open_time']),
            "close": float(df.iloc[i]['close']),
            "rsi": float(rsi.iloc[i]) if not pd.isna(rsi.iloc[i]) else None,
            "ma": float(ma.iloc[i]) if not pd.isna(ma.iloc[i]) else None,
            "ema": float(ema.iloc[i]) if not pd.isna(ema.iloc[i]) else None,
            "macd": float(macd_line.iloc[i]) if not pd.isna(macd_line.iloc[i]) else None,
            "macd_signal": float(macd_signal_line.iloc[i]) if not pd.isna(macd_signal_line.iloc[i]) else None,
            "macd_hist": float(macd_hist.iloc[i]) if not pd.isna(macd_hist.iloc[i]) else None,
            "bb_upper": float(bb_upper.iloc[i]) if not pd.isna(bb_upper.iloc[i]) else None,
            "bb_middle": float(bb_ma.iloc[i]) if not pd.isna(bb_ma.iloc[i]) else None,
            "bb_lower": float(bb_lower.iloc[i]) if not pd.isna(bb_lower.iloc[i]) else None,
        })
    return {
        "symbol": symbol.upper(),
        "interval": interval,
        "params": {
            "rsi_period": rsi_period,
            "ma_period": ma_period,
            "ema_period": ema_period,
            "macd_fast": macd_fast,
            "macd_slow": macd_slow,
            "macd_signal": macd_signal,
            "bb_period": bb_period,
            "bb_std": bb_std
        },
        "data": indicators
    }

@router.post("/train-lstm")
def api_train_lstm(
    symbol: str = Query("BTCUSDT"),
    interval: str = Query("1m"),
    limit: int = Query(300),
    n_past: int = Query(30),
    n_epochs: int = Query(30)
):
    """
    Huấn luyện và lưu model LSTM từ dữ liệu nến đã lưu trong MongoDB (chuẩn RESTful: dùng POST)
    Truyền các tham số vào query string như ?symbol=BTCUSDT&interval=5m...
    """
    db = get_database()
    collection_name = f"kline_{symbol.lower()}_{interval}"
    collection = db[collection_name]
    data = list(collection.find().sort('open_time', -1).limit(limit))
    data = data[::-1]
    import pandas as pd
    df = pd.DataFrame(data)
    if df.empty or "close" not in df:
        return {"error": "No data for training"}
    closes = df['close'].tolist()
    from data_analysis.price_prediction import train_and_save_lstm
    model_path = train_and_save_lstm(closes, n_past=n_past, n_epochs=n_epochs)
    return {"symbol": symbol.upper(), "interval": interval, "model_path": model_path}

@router.get("/predict-lstm-model")
def api_predict_lstm_model(symbol: str = "BTCUSDT", interval: str = "1m", limit: int = 100, n_past: int = 30):
    """
    Load model LSTM đã lưu và dự đoán giá close tiếp theo từ dữ liệu nến mới nhất
    """
    db = get_database()
    collection_name = f"kline_{symbol.lower()}_{interval}"
    collection = db[collection_name]
    data = list(collection.find().sort('open_time', -1).limit(limit))
    data = data[::-1]
    df = pd.DataFrame(data)
    if df.empty or "close" not in df:
        return {"error": "No data for prediction"}
    closes = df['close'].tolist()
    pred = load_and_predict_lstm(closes, n_past=n_past)
    return {
        "symbol": symbol.upper(),
        "interval": interval,
        "n_past": n_past,
        "prediction": pred,
        "last_close": closes[-1] if closes else None
    }
@router.get("/trade-signals")
def api_get_trade_signals(symbol: str = None, interval: str = None, limit: int = 100):
    result = get_trade_signals(symbol, interval, limit)
    return serialize_mongo(result)

# API lưu tín hiệu (cho phép gọi ngoài flow tự động)
@router.post("/trade-signal")
def api_log_trade_signal(
    symbol: str = Query(...),
    interval: str = Query(...),
    signal: str = Query(...),
    last_close: float = Query(...),
    prediction: float = Query(...),
    stop_loss: float = Query(...),
    take_profit: float = Query(...),
    position_size: float = Query(...)
):
    log = log_trade_signal(
        symbol, interval, signal, last_close, prediction, stop_loss, take_profit, position_size
    )
    return serialize_mongo(log)