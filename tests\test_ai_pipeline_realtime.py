# test/test_ai_pipeline_realtime.py
# Test AI pipeline đa symbol, đa interval, auto fetch real-time giá nến từ Binance nếu DB chưa có
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import time
import requests
from data_analysis.price_prediction import train_and_save_lstm_multi, load_and_predict_lstm_multi
from data_analysis.backtest import backtest_signals
from db.session import get_database
from db.trade_log import log_trade_signal
from risk_management.risk_rules import check_stop_loss_take_profit, check_position_size
import pandas as pd
def fetch_all_usdt_symbols():
    url = "https://api.binance.com/api/v3/exchangeInfo"
    try:
        resp = requests.get(url, timeout=10)
        resp.raise_for_status()
        data = resp.json()
        symbols = [s["symbol"] for s in data["symbols"] if s["quoteAsset"] == "USDT" and s["status"] == "TRADING"]
        return symbols
    except Exception as e:
        print("[AI-PIPELINE] Lỗi fetch symbols:", e)
        return []

SYMBOLS = fetch_all_usdt_symbols()
INTERVALS = ["5m", "15m"]
LIMIT = 300
N_PAST = 30
N_EPOCHS = 10
FEATURES = ["close", "rsi", "ma", "macd"]
MIN_DIFF_PCT = 0.2
BALANCE = 1000

def add_indicators(df):
    if df.empty or "close" not in df:
        return df
    closes = df['close']
    delta = closes.diff()
    gain = (delta.where(delta > 0, 0)).rolling(14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
    rs = gain / loss
    df['rsi'] = 100 - (100 / (1 + rs))
    df['ma'] = closes.rolling(14).mean()
    exp1 = closes.ewm(span=12, adjust=False).mean()
    exp2 = closes.ewm(span=26, adjust=False).mean()
    df['macd'] = exp1 - exp2
    return df

def fetch_realtime_klines(symbol, interval, limit):
    url = f"https://api.binance.com/api/v3/klines?symbol={symbol}&interval={interval}&limit={limit}"
    resp = requests.get(url)
    data = resp.json()
    df = pd.DataFrame(data, columns=[
        "open_time", "open", "high", "low", "close", "volume", "close_time",
        "quote_asset_vol", "num_trades", "taker_buy_base_vol", "taker_buy_quote_vol", "ignore"
    ])
    for col in ["open", "high", "low", "close", "volume"]:
        df[col] = df[col].astype(float)
    df["open_time"] = df["open_time"].astype(int)
    return df

def test_pipeline():
    db = get_database()
    for symbol in SYMBOLS:
        for interval in INTERVALS:
            collection_name = f"kline_{symbol.lower()}_{interval}"
            collection = db[collection_name]
            data = list(collection.find().sort('open_time', -1).limit(LIMIT))
            data = data[::-1]
            if not data or len(data) < LIMIT:
                # Fetch real-time nếu thiếu hoặc chưa có
                print(f"[TEST] {symbol}-{interval}: Fetching real-time candles...")
                df = fetch_realtime_klines(symbol, interval, limit=LIMIT)
            else:
                df = pd.DataFrame(data)
            if df.empty or "close" not in df:
                print(f"[TEST] {symbol}-{interval}: Không có dữ liệu.")
                continue
            df = add_indicators(df)
            train_and_save_lstm_multi(df, FEATURES, n_past=N_PAST, n_epochs=N_EPOCHS)
            pred = load_and_predict_lstm_multi(df, FEATURES, n_past=N_PAST)
            last_row = df.iloc[-1]
            last_close = float(last_row["close"])
            diff_pct = ((pred - last_close) / last_close) * 100 if pred else 0
            if pred is None:
                print(f"[TEST] {symbol}-{interval}: Model chưa train đủ dữ liệu.")
                continue
            if diff_pct > MIN_DIFF_PCT:
                signal_type = "BUY"
            elif diff_pct < -MIN_DIFF_PCT:
                signal_type = "SELL"
            else:
                signal_type = "HOLD"
            signal = {
                "symbol": symbol,
                "interval": interval,
                "last_close": last_close,
                "prediction": pred,
                "diff_pct": diff_pct,
                "signal": signal_type,
                "features": {f: float(last_row[f]) if f in last_row else None for f in FEATURES},
                "timestamp": int(time.time()*1000)
            }
            if signal_type != "HOLD":
                sl_tp = check_stop_loss_take_profit(signal)
                pos_size = check_position_size(BALANCE)
                log_trade_signal(
                    symbol, interval, signal_type, last_close, pred,
                    sl_tp['stop_loss'], sl_tp['take_profit'], pos_size
                )
            print(f"[TEST] {symbol}-{interval}: Signal={signal_type}, Close={last_close}, Predict={pred}, Diff={diff_pct:.3f}%")
            bt = backtest_signals(symbol, interval, n_recent=100)
            print("[DEBUG] bt =", bt)
            print(f"[TEST] {symbol}-{interval}: Backtest: Winrate={bt.get('winrate', 'N/A')}%, Total profit={bt.get('total_profit', 'N/A')}")

if __name__ == "__main__":
    test_pipeline()
