# test/test_ai_pipeline_realtime.py
# Test AI pipeline với 20 cặp crypto phổ biến nhất, tối <PERSON><PERSON> hi<PERSON>u suất
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import time
import requests
from data_analysis.price_prediction import train_and_save_lstm_multi, load_and_predict_lstm_multi
from data_analysis.backtest import backtest_signals
from db.session import get_database
from db.trade_log import log_trade_signal
from risk_management.risk_rules import check_stop_loss_take_profit, check_position_size
from config.top_symbols import get_validated_top_symbols, MODEL_CONFIG, RISK_CONFIG
import pandas as pd
import concurrent.futures
from threading import Lock

# Use optimized configuration
SYMBOLS = get_validated_top_symbols(20)  # Top 20 symbols only
INTERVALS = ["5m"]  # Focus on 5m for faster testing
LIMIT = MODEL_CONFIG["limit"]
N_PAST = MODEL_CONFIG["n_past"]
N_EPOCHS = MODEL_CONFIG["n_epochs"]
FEATURES = MODEL_CONFIG["features"]
MIN_DIFF_PCT = MODEL_CONFIG["min_diff_pct"]
BALANCE = RISK_CONFIG["balance"]

# Thread safety
print_lock = Lock()

def safe_print(message):
    """Thread-safe printing"""
    with print_lock:
        print(message)

def add_indicators(df):
    if df.empty or "close" not in df:
        return df
    closes = df['close']
    delta = closes.diff()
    gain = (delta.where(delta > 0, 0)).rolling(14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
    rs = gain / loss
    df['rsi'] = 100 - (100 / (1 + rs))
    df['ma'] = closes.rolling(14).mean()
    exp1 = closes.ewm(span=12, adjust=False).mean()
    exp2 = closes.ewm(span=26, adjust=False).mean()
    df['macd'] = exp1 - exp2
    return df

def fetch_realtime_klines(symbol, interval, limit):
    url = f"https://api.binance.com/api/v3/klines?symbol={symbol}&interval={interval}&limit={limit}"
    resp = requests.get(url)
    data = resp.json()
    df = pd.DataFrame(data, columns=[
        "open_time", "open", "high", "low", "close", "volume", "close_time",
        "quote_asset_vol", "num_trades", "taker_buy_base_vol", "taker_buy_quote_vol", "ignore"
    ])
    for col in ["open", "high", "low", "close", "volume"]:
        df[col] = df[col].astype(float)
    df["open_time"] = df["open_time"].astype(int)
    return df

def process_symbol_interval(symbol, interval):
    """Process a single symbol-interval pair"""
    try:
        db = get_database()
        collection_name = f"kline_{symbol.lower()}_{interval}"
        collection = db[collection_name]

        # Try to get data from DB first
        data = list(collection.find().sort('open_time', -1).limit(LIMIT))
        data = data[::-1]

        if not data or len(data) < LIMIT // 2:  # If less than half required data
            safe_print(f"[TEST] {symbol}-{interval}: Fetching real-time candles...")
            df = fetch_realtime_klines(symbol, interval, limit=LIMIT)
        else:
            df = pd.DataFrame(data)

        if df.empty or "close" not in df:
            safe_print(f"[TEST] {symbol}-{interval}: Không có dữ liệu.")
            return None

        # Add indicators
        df = add_indicators(df)

        # Validate data before training
        df_clean = df.dropna()
        if len(df_clean) < N_PAST + 10:
            safe_print(f"[TEST] {symbol}-{interval}: Không đủ dữ liệu sạch ({len(df_clean)} rows).")
            return None

        # Train model
        model_path = train_and_save_lstm_multi(df_clean, FEATURES, n_past=N_PAST, n_epochs=N_EPOCHS)
        if not model_path:
            safe_print(f"[TEST] {symbol}-{interval}: Training failed.")
            return None

        # Make prediction
        pred = load_and_predict_lstm_multi(df_clean, FEATURES, n_past=N_PAST)
        if pred is None:
            safe_print(f"[TEST] {symbol}-{interval}: Prediction failed.")
            return None

        # Calculate signal
        last_row = df_clean.iloc[-1]
        last_close = float(last_row["close"])
        diff_pct = ((pred - last_close) / last_close) * 100

        if diff_pct > MIN_DIFF_PCT:
            signal_type = "BUY"
        elif diff_pct < -MIN_DIFF_PCT:
            signal_type = "SELL"
        else:
            signal_type = "HOLD"

        # Create signal object
        signal = {
            "symbol": symbol,
            "interval": interval,
            "last_close": last_close,
            "prediction": pred,
            "diff_pct": diff_pct,
            "signal": signal_type,
            "features": {f: float(last_row[f]) if f in last_row and not pd.isna(last_row[f]) else None for f in FEATURES},
            "timestamp": int(time.time()*1000)
        }

        # Log trade signal if not HOLD
        if signal_type != "HOLD":
            try:
                sl_tp = check_stop_loss_take_profit(signal)
                pos_size = check_position_size(BALANCE)
                log_trade_signal(
                    symbol, interval, signal_type, last_close, pred,
                    sl_tp['stop_loss'], sl_tp['take_profit'], pos_size
                )
            except Exception as e:
                safe_print(f"[WARNING] {symbol}-{interval}: Failed to log trade: {e}")

        # Backtest (optional, can be disabled for speed)
        try:
            bt = backtest_signals(symbol, interval, n_recent=50)  # Reduced for speed
            winrate = bt.get('winrate', 'N/A')
            profit = bt.get('total_profit', 'N/A')
        except Exception as e:
            winrate = 'Error'
            profit = 'Error'

        safe_print(f"[TEST] {symbol}-{interval}: Signal={signal_type}, Close={last_close:.2f}, Predict={pred:.2f}, Diff={diff_pct:.3f}%, Winrate={winrate}")

        return signal

    except Exception as e:
        safe_print(f"[ERROR] {symbol}-{interval}: {e}")
        return None

def test_pipeline():
    """Main test pipeline with improved error handling and performance"""
    safe_print(f"[TEST] Starting AI pipeline test with {len(SYMBOLS)} symbols...")
    safe_print(f"[TEST] Symbols: {', '.join(SYMBOLS[:5])}{'...' if len(SYMBOLS) > 5 else ''}")

    start_time = time.time()
    successful_signals = []
    failed_count = 0

    # Process each symbol-interval combination
    for i, symbol in enumerate(SYMBOLS):
        for interval in INTERVALS:
            safe_print(f"[TEST] Processing {i+1}/{len(SYMBOLS)}: {symbol}-{interval}")

            signal = process_symbol_interval(symbol, interval)
            if signal:
                successful_signals.append(signal)
            else:
                failed_count += 1

            # Add small delay to avoid rate limiting
            time.sleep(0.1)

    # Summary
    elapsed_time = time.time() - start_time
    total_processed = len(SYMBOLS) * len(INTERVALS)
    success_rate = len(successful_signals) / total_processed * 100

    safe_print(f"\n[SUMMARY] Pipeline completed in {elapsed_time:.1f}s")
    safe_print(f"[SUMMARY] Processed: {total_processed}, Successful: {len(successful_signals)}, Failed: {failed_count}")
    safe_print(f"[SUMMARY] Success rate: {success_rate:.1f}%")

    # Show signal distribution
    buy_signals = sum(1 for s in successful_signals if s['signal'] == 'BUY')
    sell_signals = sum(1 for s in successful_signals if s['signal'] == 'SELL')
    hold_signals = sum(1 for s in successful_signals if s['signal'] == 'HOLD')

    safe_print(f"[SUMMARY] Signals - BUY: {buy_signals}, SELL: {sell_signals}, HOLD: {hold_signals}")

    return successful_signals

if __name__ == "__main__":
    test_pipeline()
