# tests/test_top20_optimized.py
# Optimized test for top 20 crypto pairs with better performance
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import time
import requests
from data_analysis.price_prediction import train_and_save_lstm_multi, load_and_predict_lstm_multi
from db.session import get_database
from config.top_symbols import get_validated_top_symbols, MODEL_CONFIG, RISK_CONFIG
import pandas as pd
import numpy as np

def add_indicators(df):
    """Add technical indicators to dataframe"""
    if df.empty or "close" not in df:
        return df
    
    closes = df['close'].astype(float)
    
    # RSI
    delta = closes.diff()
    gain = (delta.where(delta > 0, 0)).rolling(14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
    rs = gain / loss
    df['rsi'] = 100 - (100 / (1 + rs))
    
    # Moving Average
    df['ma'] = closes.rolling(14).mean()
    
    # MACD
    exp1 = closes.ewm(span=12, adjust=False).mean()
    exp2 = closes.ewm(span=26, adjust=False).mean()
    df['macd'] = exp1 - exp2
    
    return df

def fetch_realtime_klines(symbol, interval, limit):
    """Fetch real-time klines from Binance API"""
    url = f"https://api.binance.com/api/v3/klines?symbol={symbol}&interval={interval}&limit={limit}"
    try:
        resp = requests.get(url, timeout=10)
        resp.raise_for_status()
        data = resp.json()
        
        df = pd.DataFrame(data, columns=[
            "open_time", "open", "high", "low", "close", "volume", "close_time",
            "quote_asset_vol", "num_trades", "taker_buy_base_vol", "taker_buy_quote_vol", "ignore"
        ])
        
        # Convert to proper types
        for col in ["open", "high", "low", "close", "volume"]:
            df[col] = df[col].astype(float)
        df["open_time"] = df["open_time"].astype(int)
        
        return df
    except Exception as e:
        print(f"[ERROR] Failed to fetch {symbol}-{interval}: {e}")
        return pd.DataFrame()

def test_single_symbol(symbol, interval="5m"):
    """Test AI pipeline for a single symbol"""
    print(f"\n[TEST] Processing {symbol}-{interval}...")
    
    # Configuration
    limit = MODEL_CONFIG["limit"]
    n_past = MODEL_CONFIG["n_past"]
    n_epochs = MODEL_CONFIG["n_epochs"]
    features = MODEL_CONFIG["features"]
    min_diff_pct = MODEL_CONFIG["min_diff_pct"]
    
    # Fetch data
    df = fetch_realtime_klines(symbol, interval, limit)
    if df.empty:
        print(f"[ERROR] No data for {symbol}")
        return None
    
    print(f"[INFO] Fetched {len(df)} candles")
    
    # Add indicators
    df = add_indicators(df)
    
    # Check data quality
    df_clean = df.dropna()
    print(f"[INFO] Clean data: {len(df_clean)} rows")
    
    if len(df_clean) < n_past + 10:
        print(f"[ERROR] Insufficient clean data: {len(df_clean)}")
        return None
    
    # Train model
    print(f"[INFO] Training LSTM model...")
    start_time = time.time()
    model_path = train_and_save_lstm_multi(df_clean, features, n_past=n_past, n_epochs=n_epochs)
    train_time = time.time() - start_time
    
    if not model_path:
        print(f"[ERROR] Training failed")
        return None
    
    print(f"[INFO] Training completed in {train_time:.1f}s")
    
    # Make prediction
    pred = load_and_predict_lstm_multi(df_clean, features, n_past=n_past)
    if pred is None:
        print(f"[ERROR] Prediction failed")
        return None
    
    # Calculate signal
    last_close = float(df_clean.iloc[-1]["close"])
    diff_pct = ((pred - last_close) / last_close) * 100
    
    if diff_pct > min_diff_pct:
        signal_type = "BUY"
    elif diff_pct < -min_diff_pct:
        signal_type = "SELL"
    else:
        signal_type = "HOLD"
    
    # Results
    result = {
        "symbol": symbol,
        "interval": interval,
        "last_close": last_close,
        "prediction": pred,
        "diff_pct": diff_pct,
        "signal": signal_type,
        "train_time": train_time,
        "data_quality": len(df_clean) / len(df) * 100
    }
    
    print(f"[RESULT] {symbol}: {signal_type} | Close: {last_close:.2f} | Pred: {pred:.2f} | Diff: {diff_pct:.3f}%")
    
    return result

def test_top20_batch():
    """Test all top 20 symbols in batch"""
    print("=" * 60)
    print("🚀 TESTING TOP 20 CRYPTO PAIRS - OPTIMIZED VERSION")
    print("=" * 60)
    
    # Get validated symbols
    symbols = get_validated_top_symbols(20)
    print(f"[INFO] Testing {len(symbols)} symbols: {', '.join(symbols[:5])}...")
    
    start_time = time.time()
    results = []
    
    for i, symbol in enumerate(symbols):
        print(f"\n[PROGRESS] {i+1}/{len(symbols)}")
        result = test_single_symbol(symbol)
        
        if result:
            results.append(result)
        
        # Small delay to avoid rate limiting
        time.sleep(0.2)
    
    # Summary
    elapsed_time = time.time() - start_time
    success_rate = len(results) / len(symbols) * 100
    
    print("\n" + "=" * 60)
    print("📊 SUMMARY RESULTS")
    print("=" * 60)
    print(f"Total time: {elapsed_time:.1f}s")
    print(f"Processed: {len(symbols)}, Successful: {len(results)}")
    print(f"Success rate: {success_rate:.1f}%")
    
    if results:
        # Signal distribution
        buy_count = sum(1 for r in results if r['signal'] == 'BUY')
        sell_count = sum(1 for r in results if r['signal'] == 'SELL')
        hold_count = sum(1 for r in results if r['signal'] == 'HOLD')
        
        print(f"\nSignal distribution:")
        print(f"  BUY:  {buy_count:2d} ({buy_count/len(results)*100:.1f}%)")
        print(f"  SELL: {sell_count:2d} ({sell_count/len(results)*100:.1f}%)")
        print(f"  HOLD: {hold_count:2d} ({hold_count/len(results)*100:.1f}%)")
        
        # Top predictions
        print(f"\nTop BUY signals:")
        buy_signals = [r for r in results if r['signal'] == 'BUY']
        buy_signals.sort(key=lambda x: x['diff_pct'], reverse=True)
        for r in buy_signals[:3]:
            print(f"  {r['symbol']:8s}: +{r['diff_pct']:5.2f}% (${r['last_close']:8.2f} -> ${r['prediction']:8.2f})")
        
        print(f"\nTop SELL signals:")
        sell_signals = [r for r in results if r['signal'] == 'SELL']
        sell_signals.sort(key=lambda x: x['diff_pct'])
        for r in sell_signals[:3]:
            print(f"  {r['symbol']:8s}: {r['diff_pct']:6.2f}% (${r['last_close']:8.2f} -> ${r['prediction']:8.2f})")
        
        # Performance stats
        avg_train_time = np.mean([r['train_time'] for r in results])
        avg_data_quality = np.mean([r['data_quality'] for r in results])
        
        print(f"\nPerformance stats:")
        print(f"  Avg training time: {avg_train_time:.1f}s")
        print(f"  Avg data quality: {avg_data_quality:.1f}%")
    
    print("=" * 60)
    return results

if __name__ == "__main__":
    # Run the optimized test
    results = test_top20_batch()
    
    # Save results to file
    if results:
        import json
        timestamp = int(time.time())
        filename = f"test_results_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n💾 Results saved to: {filename}")
