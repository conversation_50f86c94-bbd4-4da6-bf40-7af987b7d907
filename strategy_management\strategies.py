from db.session import get_database
from data_analysis.price_prediction import load_and_predict_lstm, load_and_predict_lstm_multi
from db.trade_log import log_trade_signal
from risk_management.risk_rules import check_stop_loss_take_profit, check_position_size
import pandas as pd
import time

# ADD: import risk và log
from db.trade_log import log_trade_signal
from risk_management.risk_rules import check_stop_loss_take_profit, check_position_size

def ai_trade_flow(
    symbol: str = "BTCUSDT",
    interval: str = "5m",
    limit: int = 100,
    n_past: int = 30,
    min_diff_pct: float = 0.2,
    balance: float = 1000  # <-- truyền số dư vào đây!
):
    db = get_database()
    collection_name = f"kline_{symbol.lower()}_{interval}"
    collection = db[collection_name]
    data = list(collection.find().sort('open_time', -1).limit(limit))
    data = data[::-1]
    df = pd.DataFrame(data)
    if df.empty or "close" not in df:
        print("[AI-Trade] Không có dữ liệu để trade.")
        return None
    closes = df['close'].tolist()
    pred = load_and_predict_lstm(closes, n_past=n_past)
    last_close = closes[-1]
    if pred is None:
        print("[AI-Trade] Model LSTM chưa huấn luyện hoặc không đủ dữ liệu.")
        return None
    diff_pct = ((pred - last_close) / last_close) * 100
    if diff_pct > min_diff_pct:
        signal_type = "BUY"
    elif diff_pct < -min_diff_pct:
        signal_type = "SELL"
    else:
        signal_type = "HOLD"
    signal = {
        "symbol": symbol,
        "interval": interval,
        "last_close": last_close,
        "prediction": pred,
        "diff_pct": diff_pct,
        "signal": signal_type,
        "timestamp": int(time.time()*1000)
    }
    if signal_type != "HOLD":
        sl_tp = check_stop_loss_take_profit(signal)
        pos_size = check_position_size(balance)
        # Lưu tín hiệu vào DB cho backtest, thống kê sau này
        log_trade_signal(
            symbol, interval, signal_type, last_close, pred,
            sl_tp['stop_loss'], sl_tp['take_profit'], pos_size
        )
    return signal
def ai_trade_multi_feature_flow(symbol: str = "BTCUSDT", interval: str = "5m", limit: int = 100, n_past: int = 30, min_diff_pct: float = 0.2, balance: float = 1000, features: list = ["close", "rsi", "ma", "macd"]):
    db = get_database()
    collection_name = f"kline_{symbol.lower()}_{interval}"
    collection = db[collection_name]
    data = list(collection.find().sort('open_time', -1).limit(limit))
    data = data[::-1]
    df = pd.DataFrame(data)
    pred = load_and_predict_lstm_multi(df, features, n_past=n_past)
    last_row = df.iloc[-1]
    last_close = float(last_row["close"])
    diff_pct = ((pred - last_close) / last_close) * 100 if pred else 0
    if pred is None:
        print("[AI-Trade-Multi] Model LSTM multi chưa train hoặc không đủ dữ liệu.")
        return None
    if diff_pct > min_diff_pct:
        signal_type = "BUY"
    elif diff_pct < -min_diff_pct:
        signal_type = "SELL"
    else:
        signal_type = "HOLD"
    signal = {
        "symbol": symbol,
        "interval": interval,
        "last_close": last_close,
        "prediction": pred,
        "diff_pct": diff_pct,
        "signal": signal_type,
        "features": {f: float(last_row[f]) if f in last_row else None for f in features},
        "timestamp": int(time.time()*1000)
    }
    if signal_type != "HOLD":
        sl_tp = check_stop_loss_take_profit(signal)
        pos_size = check_position_size(balance)
        log_trade_signal(
            symbol, interval, signal_type, last_close, pred,
            sl_tp['stop_loss'], sl_tp['take_profit'], pos_size
        )
        print(f"[AI-Trade-Multi] Đã lưu tín hiệu vào DB: {signal_type} @ {last_close}")
    return signal