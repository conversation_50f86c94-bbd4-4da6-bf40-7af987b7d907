# utils/performance_monitor.py
# Performance monitoring and optimization utilities
import time
import psutil
import gc
import os
from functools import wraps
from typing import Dict, Any, List
import json

class PerformanceMonitor:
    """Monitor system performance during trading operations"""
    
    def __init__(self):
        self.metrics = []
        self.start_time = None
        self.process = psutil.Process()
    
    def start_monitoring(self):
        """Start performance monitoring"""
        self.start_time = time.time()
        self.metrics = []
        gc.collect()  # Clean up memory before starting
    
    def record_metric(self, operation: str, duration: float, memory_usage: float = None):
        """Record a performance metric"""
        if memory_usage is None:
            memory_usage = self.get_memory_usage()
        
        metric = {
            "operation": operation,
            "duration": duration,
            "memory_mb": memory_usage,
            "timestamp": time.time()
        }
        self.metrics.append(metric)
    
    def get_memory_usage(self) -> float:
        """Get current memory usage in MB"""
        return self.process.memory_info().rss / 1024 / 1024
    
    def get_cpu_usage(self) -> float:
        """Get current CPU usage percentage"""
        return self.process.cpu_percent()
    
    def get_summary(self) -> Dict[str, Any]:
        """Get performance summary"""
        if not self.metrics:
            return {}
        
        total_time = time.time() - self.start_time if self.start_time else 0
        durations = [m["duration"] for m in self.metrics]
        memory_usage = [m["memory_mb"] for m in self.metrics]
        
        return {
            "total_time": total_time,
            "total_operations": len(self.metrics),
            "avg_operation_time": sum(durations) / len(durations),
            "max_operation_time": max(durations),
            "min_operation_time": min(durations),
            "avg_memory_usage": sum(memory_usage) / len(memory_usage),
            "max_memory_usage": max(memory_usage),
            "operations_per_second": len(self.metrics) / total_time if total_time > 0 else 0
        }
    
    def save_report(self, filename: str):
        """Save performance report to file"""
        report = {
            "summary": self.get_summary(),
            "detailed_metrics": self.metrics,
            "system_info": {
                "cpu_count": psutil.cpu_count(),
                "memory_total_gb": psutil.virtual_memory().total / 1024 / 1024 / 1024,
                "python_version": os.sys.version
            }
        }
        
        with open(filename, 'w') as f:
            json.dump(report, f, indent=2, default=str)

# Global monitor instance
monitor = PerformanceMonitor()

def performance_tracker(operation_name: str):
    """Decorator to track performance of functions"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            start_memory = monitor.get_memory_usage()
            
            try:
                result = func(*args, **kwargs)
                success = True
            except Exception as e:
                result = None
                success = False
                raise e
            finally:
                duration = time.time() - start_time
                end_memory = monitor.get_memory_usage()
                
                monitor.record_metric(
                    f"{operation_name}{'_success' if success else '_failed'}", 
                    duration, 
                    end_memory
                )
            
            return result
        return wrapper
    return decorator

class MemoryOptimizer:
    """Memory optimization utilities"""
    
    @staticmethod
    def cleanup():
        """Force garbage collection"""
        gc.collect()
    
    @staticmethod
    def get_memory_info():
        """Get detailed memory information"""
        process = psutil.Process()
        memory_info = process.memory_info()
        
        return {
            "rss_mb": memory_info.rss / 1024 / 1024,  # Resident Set Size
            "vms_mb": memory_info.vms / 1024 / 1024,  # Virtual Memory Size
            "percent": process.memory_percent(),
            "available_mb": psutil.virtual_memory().available / 1024 / 1024
        }
    
    @staticmethod
    def optimize_dataframe(df):
        """Optimize pandas DataFrame memory usage"""
        import pandas as pd
        
        # Convert object columns to category if they have few unique values
        for col in df.select_dtypes(include=['object']):
            if df[col].nunique() / len(df) < 0.5:  # Less than 50% unique values
                df[col] = df[col].astype('category')
        
        # Downcast numeric types
        for col in df.select_dtypes(include=['int']):
            df[col] = pd.to_numeric(df[col], downcast='integer')
        
        for col in df.select_dtypes(include=['float']):
            df[col] = pd.to_numeric(df[col], downcast='float')
        
        return df

class BatchProcessor:
    """Batch processing utilities for handling large datasets"""
    
    def __init__(self, batch_size: int = 10):
        self.batch_size = batch_size
    
    def process_in_batches(self, items: List, process_func, **kwargs):
        """Process items in batches to manage memory"""
        results = []
        
        for i in range(0, len(items), self.batch_size):
            batch = items[i:i + self.batch_size]
            
            # Process batch
            batch_results = []
            for item in batch:
                try:
                    result = process_func(item, **kwargs)
                    if result:
                        batch_results.append(result)
                except Exception as e:
                    print(f"[ERROR] Failed to process {item}: {e}")
            
            results.extend(batch_results)
            
            # Clean up memory after each batch
            MemoryOptimizer.cleanup()
            
            # Optional: Add delay between batches
            if kwargs.get('delay', 0) > 0:
                time.sleep(kwargs['delay'])
        
        return results

# Usage examples and utilities
def optimize_tensorflow_memory():
    """Optimize TensorFlow memory usage"""
    try:
        import tensorflow as tf
        
        # Enable memory growth for GPU
        gpus = tf.config.experimental.list_physical_devices('GPU')
        if gpus:
            for gpu in gpus:
                tf.config.experimental.set_memory_growth(gpu, True)
        
        # Set CPU thread optimization
        tf.config.threading.set_intra_op_parallelism_threads(0)  # Use all available cores
        tf.config.threading.set_inter_op_parallelism_threads(0)
        
        print("[INFO] TensorFlow memory optimization applied")
    except ImportError:
        print("[WARNING] TensorFlow not available for optimization")
    except Exception as e:
        print(f"[WARNING] TensorFlow optimization failed: {e}")

def create_performance_report(results: List[Dict], filename: str = None):
    """Create a comprehensive performance report"""
    if not filename:
        filename = f"performance_report_{int(time.time())}.json"
    
    # Analyze results
    if results:
        training_times = [r.get('train_time', 0) for r in results if 'train_time' in r]
        data_qualities = [r.get('data_quality', 0) for r in results if 'data_quality' in r]
        
        report = {
            "timestamp": time.time(),
            "total_symbols": len(results),
            "performance_metrics": {
                "avg_training_time": sum(training_times) / len(training_times) if training_times else 0,
                "max_training_time": max(training_times) if training_times else 0,
                "min_training_time": min(training_times) if training_times else 0,
                "avg_data_quality": sum(data_qualities) / len(data_qualities) if data_qualities else 0
            },
            "signal_distribution": {
                "BUY": sum(1 for r in results if r.get('signal') == 'BUY'),
                "SELL": sum(1 for r in results if r.get('signal') == 'SELL'),
                "HOLD": sum(1 for r in results if r.get('signal') == 'HOLD')
            },
            "system_info": MemoryOptimizer.get_memory_info(),
            "detailed_results": results
        }
        
        with open(filename, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        print(f"[INFO] Performance report saved to: {filename}")
        return report
    
    return None
