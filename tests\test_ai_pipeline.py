# test/test_ai_pipeline.py
# Test: Chạy pipeline AI cho 1-2 cặp coin USDT và in ra tín hiệu, backtest
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import time
from data_analysis.price_prediction import train_and_save_lstm_multi, load_and_predict_lstm_multi
from data_analysis.backtest import backtest_signals
from db.session import get_database
from db.trade_log import log_trade_signal
from risk_management.risk_rules import check_stop_loss_take_profit, check_position_size
import pandas as pd

SYMBOLS = ["BTCUSDT", "ETHUSDT"]
INTERVAL = "5m"
LIMIT = 300
N_PAST = 30
N_EPOCHS = 10
FEATURES = ["close", "rsi", "ma", "macd"]
MIN_DIFF_PCT = 0.2
BALANCE = 1000

def add_indicators(df):
    if df.empty or "close" not in df:
        return df
    closes = df['close']
    delta = closes.diff()
    gain = (delta.where(delta > 0, 0)).rolling(14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
    rs = gain / loss
    df['rsi'] = 100 - (100 / (1 + rs))
    df['ma'] = closes.rolling(14).mean()
    exp1 = closes.ewm(span=12, adjust=False).mean()
    exp2 = closes.ewm(span=26, adjust=False).mean()
    df['macd'] = exp1 - exp2
    return df

def test_pipeline():
    db = get_database()
    for symbol in SYMBOLS:
        collection_name = f"kline_{symbol.lower()}_{INTERVAL}"
        collection = db[collection_name]
        data = list(collection.find().sort('open_time', -1).limit(LIMIT))
        data = data[::-1]
        df = pd.DataFrame(data)
        if df.empty or "close" not in df:
            print(f"[TEST] {symbol}: Không có dữ liệu.")
            continue
        df = add_indicators(df)
        train_and_save_lstm_multi(df, FEATURES, n_past=N_PAST, n_epochs=N_EPOCHS)
        pred = load_and_predict_lstm_multi(df, FEATURES, n_past=N_PAST)
        last_row = df.iloc[-1]
        last_close = float(last_row["close"])
        diff_pct = ((pred - last_close) / last_close) * 100 if pred else 0
        if pred is None:
            print(f"[TEST] {symbol}: Model chưa train đủ dữ liệu.")
            continue
        if diff_pct > MIN_DIFF_PCT:
            signal_type = "BUY"
        elif diff_pct < -MIN_DIFF_PCT:
            signal_type = "SELL"
        else:
            signal_type = "HOLD"
        signal = {
            "symbol": symbol,
            "interval": INTERVAL,
            "last_close": last_close,
            "prediction": pred,
            "diff_pct": diff_pct,
            "signal": signal_type,
            "features": {f: float(last_row[f]) if f in last_row else None for f in FEATURES},
            "timestamp": int(time.time()*1000)
        }
        if signal_type != "HOLD":
            sl_tp = check_stop_loss_take_profit(signal)
            pos_size = check_position_size(BALANCE)
            log_trade_signal(
                symbol, INTERVAL, signal_type, last_close, pred,
                sl_tp['stop_loss'], sl_tp['take_profit'], pos_size
            )
        print(f"[TEST] {symbol}: Signal={signal_type}, Close={last_close}, Predict={pred}, Diff={diff_pct:.3f}%")
        bt = backtest_signals(symbol, INTERVAL, n_recent=100)
        print(f"[TEST] {symbol}: Backtest: Winrate={bt['winrate']:.2f}%, Total profit={bt['total_profit']:.4f}")

if __name__ == "__main__":
    test_pipeline()
