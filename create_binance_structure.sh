#!/bin/bash

# Create app directory and files
mkdir -p app
touch app/__init__.py
touch app/main.py
touch app/api_router.py
touch app/deps.py

# Create core directory and files
mkdir -p core
touch core/config.py
touch core/settings.py
touch core/security.py

# Create data_collection directory and files
mkdir -p data_collection
touch data_collection/__init__.py
touch data_collection/binance_api.py
touch data_collection/data_storage.py

# Create data_analysis directory and files
mkdir -p data_analysis
touch data_analysis/__init__.py
touch data_analysis/preprocessing.py
touch data_analysis/price_prediction.py
touch data_analysis/whale_analysis.py
touch data_analysis/sentiment_analysis.py
touch data_analysis/technical_analysis.py

# Create strategy_management directory and files
mkdir -p strategy_management
touch strategy_management/__init__.py
touch strategy_management/strategies.py

# Create trade_execution directory and files
mkdir -p trade_execution
touch trade_execution/__init__.py
touch trade_execution/order_manager.py

# Create risk_management directory and files
mkdir -p risk_management
touch risk_management/__init__.py
touch risk_management/risk_rules.py

# Create logging_monitoring directory and files
mkdir -p logging_monitoring
touch logging_monitoring/__init__.py
touch logging_monitoring/logger.py

# Create models directory and files
mkdir -p models
touch models/__init__.py
touch models/candle.py
touch models/trade.py
touch models/user.py

# Create db directory and files
mkdir -p db
touch db/__init__.py
touch db/session.py
touch db/crud.py

# Create tests directory and files
mkdir -p tests
touch tests/__init__.py
touch tests/test_api.py
touch tests/test_analysis.py

# Create root level files
touch requirements.txt
touch .env
touch README.md

echo "Binance Trading Bot directory structure created successfully!"
echo "Project structure:"
echo "├── app/                    # RESTful API hoặc WebSocket endpoints"
echo "│   ├── __init__.py"
echo "│   ├── main.py             # Entry point, khởi chạy API backend"
echo "│   ├── api_router.py       # Định nghĩa các route chính"
echo "│   └── deps.py             # Các hàm phụ trợ dùng chung"
echo "├── core/                   # Logic cốt lõi, cấu hình"
echo "│   ├── config.py           # Đọc .env, quản lý config"
echo "│   ├── settings.py         # Cấu hình global, logging"
echo "│   └── security.py         # API key & xác thực"
echo "├── data_collection/        # Thu thập dữ liệu Binance"
echo "│   ├── binance_api.py      # REST/WebSocket Binance"
echo "│   └── data_storage.py     # Lưu dữ liệu về DB/MongoDB/CSV"
echo "├── data_analysis/          # AI/ML, phân tích dữ liệu"
echo "│   ├── preprocessing.py"
echo "│   ├── price_prediction.py"
echo "│   ├── whale_analysis.py"
echo "│   ├── sentiment_analysis.py"
echo "│   └── technical_analysis.py"
echo "├── strategy_management/    # Logic chiến lược giao dịch"
echo "│   └── strategies.py"
echo "├── trade_execution/        # Đặt lệnh Binance"
echo "│   └── order_manager.py"
echo "├── risk_management/        # Quản lý rủi ro (SL/TP, DCA...)"
echo "│   └── risk_rules.py"
echo "├── logging_monitoring/     # Ghi nhật ký, alert"
echo "│   └── logger.py"
echo "├── models/                 # Định nghĩa Pydantic/ORM cho dữ liệu"
echo "│   ├── candle.py"
echo "│   ├── trade.py"
echo "│   └── user.py"
echo "├── db/                     # Database utils (MongoDB, SQLite...)"
echo "│   ├── session.py"
echo "│   └── crud.py"
echo "├── tests/                  # Unit/integration test"
echo "│   ├── test_api.py"
echo "│   └── test_analysis.py"
echo "├── requirements.txt"
echo "├── .env                    # API keys, DB connection string..."
echo "└── README.md"
