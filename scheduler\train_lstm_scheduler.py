# scheduler/train_lstm_scheduler.py
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import time
from apscheduler.schedulers.background import BackgroundScheduler
from data_analysis.price_prediction import train_and_save_lstm
from db.session import get_database
import pandas as pd

# Cấu hình: train nhiều symbol & interval cùng lúc
SYMBOLS = ["BTCUSDT", "ETHUSDT", "BNBUSDT"]
INTERVALS = ["5m", "15m"]
LIMIT = 300
N_PAST = 30
N_EPOCHS = 30

def retrain_lstm_job():
    db = get_database()
    for symbol in SYMBOLS:
        for interval in INTERVALS:
            collection_name = f"kline_{symbol.lower()}_{interval}"
            collection = db[collection_name]
            data = list(collection.find().sort('open_time', -1).limit(LIMIT))
            data = data[::-1]
            df = pd.DataFrame(data)
            if df.empty or "close" not in df:
                print(f"[LSTM-Scheduler] {symbol}-{interval}: Không có dữ liệu để train.")
                continue
            closes = df['close'].tolist()
            path = train_and_save_lstm(closes, n_past=N_PAST, n_epochs=N_EPOCHS)
            print(f"[LSTM-Scheduler] Model retrained & saved for {symbol}-{interval}: {path}")

if __name__ == "__main__":
    scheduler = BackgroundScheduler()
    scheduler.add_job(retrain_lstm_job, 'interval', hours=4, next_run_time=None)
    scheduler.start()
    print("[LSTM-Scheduler] Đang chạy... (ấn Ctrl+C để dừng)")
    try:
        while True:
            time.sleep(60)
    except (KeyboardInterrupt, SystemExit):
        scheduler.shutdown()
        print("[LSTM-Scheduler] Đã dừng.")
