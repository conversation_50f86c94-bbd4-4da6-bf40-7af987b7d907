import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import time
from apscheduler.schedulers.background import BackgroundScheduler
from data_analysis.price_prediction import train_and_save_lstm_multi, load_and_predict_lstm_multi
from data_analysis.backtest import backtest_signals
from db.session import get_database
from db.trade_log import log_trade_signal
import pandas as pd
import requests

INTERVALS = ["5m"]
LIMIT = 300
N_PAST = 30
N_EPOCHS = 20
FEATURES = ["close", "rsi", "ma", "macd"]
MIN_DIFF_PCT = 0.2
BALANCE = 1000

# Lấy toàn bộ symbol có USDT từ Binance, auto cập nhật
def fetch_all_usdt_symbols():
    url = "https://api.binance.com/api/v3/exchangeInfo"
    try:
        resp = requests.get(url, timeout=10)
        resp.raise_for_status()
        data = resp.json()
        symbols = [s["symbol"] for s in data["symbols"] if s["quoteAsset"] == "USDT" and s["status"] == "TRADING"]
        return symbols
    except Exception as e:
        print("[AI-PIPELINE] Lỗi fetch symbols:", e)
        return []

def add_indicators(df, rsi_period=14, ma_period=14, macd_fast=12, macd_slow=26, macd_signal=9):
    if df.empty or "close" not in df:
        return df
    closes = df['close']
    delta = closes.diff()
    gain = (delta.where(delta > 0, 0)).rolling(rsi_period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(rsi_period).mean()
    rs = gain / loss
    df['rsi'] = 100 - (100 / (1 + rs))
    df['ma'] = closes.rolling(ma_period).mean()
    exp1 = closes.ewm(span=macd_fast, adjust=False).mean()
    exp2 = closes.ewm(span=macd_slow, adjust=False).mean()
    df['macd'] = exp1 - exp2
    return df

def ai_pipeline_job():
    SYMBOLS = fetch_all_usdt_symbols()
    print(f"[AI-PIPELINE] Đang quét {len(SYMBOLS)} cặp USDT...")
    db = get_database()
    for symbol in SYMBOLS:
        for interval in INTERVALS:
            collection_name = f"kline_{symbol.lower()}_{interval}"
            collection = db[collection_name]
            data = list(collection.find().sort('open_time', -1).limit(LIMIT))
            data = data[::-1]
            df = pd.DataFrame(data)
            if df.empty or "close" not in df:
                print(f"[AI-PIPELINE] {symbol}-{interval}: Không có dữ liệu.")
                continue
            df = add_indicators(df)
            model_path = train_and_save_lstm_multi(df, FEATURES, n_past=N_PAST, n_epochs=N_EPOCHS)
            print(f"[AI-PIPELINE] Retrained & saved: {symbol}-{interval}")
            pred = load_and_predict_lstm_multi(df, FEATURES, n_past=N_PAST)
            last_row = df.iloc[-1]
            last_close = float(last_row["close"])
            diff_pct = ((pred - last_close) / last_close) * 100 if pred else 0
            if pred is None:
                continue
            if diff_pct > MIN_DIFF_PCT:
                signal_type = "BUY"
            elif diff_pct < -MIN_DIFF_PCT:
                signal_type = "SELL"
            else:
                signal_type = "HOLD"
            from risk_management.risk_rules import check_stop_loss_take_profit, check_position_size
            signal = {
                "symbol": symbol,
                "interval": interval,
                "last_close": last_close,
                "prediction": pred,
                "diff_pct": diff_pct,
                "signal": signal_type,
                "features": {f: float(last_row[f]) if f in last_row else None for f in FEATURES},
                "timestamp": int(time.time()*1000)
            }
            if signal_type != "HOLD":
                sl_tp = check_stop_loss_take_profit(signal)
                pos_size = check_position_size(BALANCE)
                log_trade_signal(
                    symbol, interval, signal_type, last_close, pred,
                    sl_tp['stop_loss'], sl_tp['take_profit'], pos_size
                )
                print(f"[AI-PIPELINE] Log signal {signal_type}: {symbol}-{interval} @ {last_close}")
            bt = backtest_signals(symbol, interval, n_recent=100)
            print(f"[AI-PIPELINE] Backtest {symbol}-{interval}: Winrate={bt['winrate']:.2f}%, Total profit={bt['total_profit']:.4f}")

if __name__ == "__main__":
    scheduler = BackgroundScheduler()
    scheduler.add_job(ai_pipeline_job, 'interval', hours=4, next_run_time=None)
    scheduler.start()
    print("[AI-PIPELINE] Đang chạy... (ấn Ctrl+C để dừng)")
    try:
        while True:
            time.sleep(60)
    except (KeyboardInterrupt, SystemExit):
        scheduler.shutdown()
        print("[AI-PIPELINE] Đã dừng.")
