"""
Module: db/session.py
Chức năng: <PERSON><PERSON><PERSON> nối MongoDB dùng biến môi trường từ .env, cung cấp hàm get_database cho các module kh<PERSON>c sử dụng.
"""
import os
from dotenv import load_dotenv
from pymongo import MongoClient

load_dotenv()  # Load biến môi trường từ file .env

MONGO_URI = os.getenv('MONGO_URI')
MONGO_DB_NAME = os.getenv('MONGO_DB_NAME', 'dataBot')

client = MongoClient(MONGO_URI)
db = client[MONGO_DB_NAME]

def get_database():
    """Trả về đối tượng database để sử dụng trong các module khác."""
    return db
