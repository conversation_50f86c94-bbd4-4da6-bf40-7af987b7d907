from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from dotenv import load_dotenv
from db.session import get_database
from app.api_router import router  # CHÚ Ý: đường dẫn import

load_dotenv()

app = FastAPI(
    title="Binance Trading Bot Backend API",
    description="REST API backend cho bot giao dịch Binance, tương tác với Next.js frontend",
    version="0.1.0"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # <PERSON><PERSON> thể lấy từ env nếu muốn
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
def read_root():
    return {"msg": "Binance Trading Bot Backend API is running"}

@app.get("/mongo-test")
def mongo_test():
    db = get_database()
    collections = db.list_collection_names()
    return {"collections": collections}

# ĐĂNG KÝ router các route nâng cao
app.include_router(router)
